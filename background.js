// background.js
chrome.runtime.onInstalled.addListener(() => {
    // Create context menu
    chrome.contextMenus.create({
        id: "readSelection",
        title: "Read selected text",
        contexts: ["selection"]
    });
    
    chrome.contextMenus.create({
        id: "readPage",
        title: "Read entire page",
        contexts: ["page"]
    });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
    chrome.storage.sync.get(['voice', 'speed'], (result) => {
        const voice = result.voice || 'en-US-Neural2-A';
        const speed = result.speed || 1.0;
        
        if (info.menuItemId === "readSelection") {
            chrome.tabs.sendMessage(tab.id, {
                action: 'readSelection',
                voice: voice,
                speed: parseFloat(speed)
            });
        } else if (info.menuItemId === "readPage") {
            chrome.tabs.sendMessage(tab.id, {
                action: 'readPage',
                voice: voice,
                speed: parseFloat(speed)
            });
        }
    });
});