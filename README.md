# Enhanced Google TTS Chrome Extension

A powerful Chrome extension that converts web page text to speech using Google Cloud Text-to-Speech API, now with **streaming playback** and **real-time text highlighting**.

## 🆕 New Features

### 🎵 Streaming TTS
- **Progressive Loading**: Audio chunks are processed and played concurrently
- **No More Waiting**: Start hearing the first words while the rest is still being processed
- **Smooth Playback**: Minimal gaps between chunks for a natural listening experience
- **Real-time Progress**: Visual indicator shows processing and playback progress

### 🎯 Text Highlighting
- **Follow Along**: Text is highlighted in real-time as it's being read
- **Smart Matching**: Advanced algorithm matches audio chunks to page text
- **Visual Feedback**: Smooth animations and attractive highlighting effects
- **Auto Scroll**: Page automatically scrolls to keep highlighted text visible

### ⏯️ Enhanced Controls
- **Pause/Resume**: Full control over playback without losing position
- **Better UI**: Improved popup interface with new control buttons
- **Status Indicators**: Clear visual feedback for all playback states

## 🚀 How It Works

### Before (Old Version)
1. Process ALL text chunks ⏳
2. Wait for everything to complete ⏳
3. Start playback 🔊

### After (Enhanced Version)
1. Start processing first chunk ⚡
2. Begin playback immediately 🔊
3. Process remaining chunks in parallel ⚡
4. Highlight text as it plays 🎯

## 📋 Features

- **Multiple Voice Options**: Choose from premium HD voices and standard neural voices
- **Adjustable Speed**: Control playback speed from 0.25x to 4.0x
- **Context Menu Integration**: Right-click to read selected text or entire page
- **Smart Text Extraction**: Intelligently extracts readable content from web pages
- **Error Handling**: Robust error handling with graceful fallbacks
- **Memory Efficient**: Proper cleanup of resources and DOM elements

## 🛠️ Installation

1. Clone or download this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension directory
5. Get a Google Cloud API key with Text-to-Speech API enabled
6. Enter your API key in the extension popup

## 🎮 Usage

### Reading Entire Pages
- Click the extension icon and select "Read Entire Page"
- Or right-click on any page and select "Read entire page"
- Watch the streaming progress indicator
- See text highlighted as it's being read

### Reading Selected Text
- Select any text on a webpage
- Right-click and choose "Read selected text"
- Or use the "Read Selection" button in the popup

### Playback Controls
- **Pause**: Temporarily stop playback (remembers position)
- **Resume**: Continue from where you left off
- **Stop**: End playback and clear highlights

## 🎨 Visual Indicators

### Streaming Progress
- **Blue indicator**: Shows current processing/playback status
- **Progress bar**: Visual representation of completion
- **Chunk counter**: "Playing chunk X/Y" information

### Text Highlighting
- **Gradient background**: Beautiful highlighting effect
- **Pulse animation**: Subtle animation draws attention
- **Auto-scroll**: Keeps highlighted text in view

## ⚙️ Technical Details

### Streaming Implementation
- **Concurrent processing**: Max 2 chunks processed simultaneously
- **Queue management**: Smart audio queue with chunk metadata
- **Error resilience**: Individual chunk failures don't stop playback

### Text Highlighting Algorithm
- **Tree walker**: Traverses DOM to find text nodes
- **Word matching**: Matches chunks to page content using word analysis
- **Smart cleanup**: Proper restoration of original DOM structure

### Performance Optimizations
- **Memory management**: Automatic cleanup of audio URLs and DOM elements
- **API efficiency**: Controlled concurrency to respect API limits
- **Resource cleanup**: Proper disposal of audio resources

## 🔧 Configuration

### Voice Settings
- Choose from 22+ premium HD voices (Chirp3)
- Standard Neural2 voices available
- Language-specific voice selection

### Speed Control
- Range: 0.25x to 4.0x playback speed
- Real-time speed adjustment
- Settings persist across sessions

## 🐛 Troubleshooting

### Common Issues
- **No audio**: Check API key and internet connection
- **Highlighting not working**: Ensure page has readable text content
- **Playback stops**: Check browser console for error messages

### Performance Tips
- Use stable internet connection for best streaming experience
- Close other audio applications to avoid conflicts
- Refresh page if highlighting becomes inconsistent

## 📝 Changelog

### Version 2.0 (Enhanced)
- ✅ Added streaming TTS functionality
- ✅ Implemented real-time text highlighting
- ✅ Added pause/resume controls
- ✅ Enhanced progress indicators
- ✅ Improved error handling
- ✅ Better memory management

### Version 1.0 (Original)
- Basic TTS functionality
- Voice selection
- Speed control
- Context menu integration

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve the extension.

## 📄 License

This project is open source and available under the MIT License.
