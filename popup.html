<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .config-section {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
        }
        button:hover {
            background: #3367d6;
        }
        .stop-btn {
            background: #ea4335;
        }
        .stop-btn:hover {
            background: #d33b2c;
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .voice-category {
            font-weight: bold;
            color: #666;
            background: #f5f5f5;
            padding: 2px 8px;
        }
    </style>
</head>
<body>
    <h3>Google TTS Reader</h3>
    
    <div class="config-section">
        <label for="apiKey">Google Cloud API Key:</label>
        <input type="text" id="apiKey" placeholder="Enter your API key">
        <button id="saveKey">Save Key</button>
    </div>
    
    <div class="config-section">
        <label for="voiceSelect">Voice:</label>
        <select id="voiceSelect">
            <optgroup label="🌟 Premium HD Voices (Chirp3)">
                <option value="en-US-Chirp3-HD-Achernar">English (US) - Achernar (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Agathon">English (US) - Agathon (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Alcyone">English (US) - Alcyone (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Almaak">English (US) - Almaak (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Altair">English (US) - Altair (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Antares">English (US) - Antares (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Arcturus">English (US) - Arcturus (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Bellatrix">English (US) - Bellatrix (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Betelgeuse">English (US) - Betelgeuse (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Canopus">English (US) - Canopus (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Capella">English (US) - Capella (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Castor">English (US) - Castor (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Deneb">English (US) - Deneb (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Fomalhaut">English (US) - Fomalhaut (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Polaris">English (US) - Polaris (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Pollux">English (US) - Pollux (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Procyon">English (US) - Procyon (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Regulus">English (US) - Regulus (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Rigel">English (US) - Rigel (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Sirius">English (US) - Sirius (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Spica">English (US) - Spica (Premium HD)</option>
                <option value="en-US-Chirp3-HD-Vega">English (US) - Vega (Premium HD)</option>
            </optgroup>
            <optgroup label="🎵 Standard Neural Voices">
                <option value="en-US-Neural2-A">English (US) - Neural2-A (Female)</option>
                <option value="en-US-Neural2-C">English (US) - Neural2-C (Female)</option>
                <option value="en-US-Neural2-D">English (US) - Neural2-D (Male)</option>
                <option value="en-US-Neural2-F">English (US) - Neural2-F (Female)</option>
                <option value="en-US-Neural2-G">English (US) - Neural2-G (Female)</option>
                <option value="en-US-Neural2-H">English (US) - Neural2-H (Female)</option>
                <option value="en-US-Neural2-I">English (US) - Neural2-I (Male)</option>
                <option value="en-US-Neural2-J">English (US) - Neural2-J (Male)</option>
            </optgroup>
        </select>
    </div>
    
    <div class="config-section">
        <label for="speedRange">Speed: <span id="speedValue">1.0</span></label>
        <input type="range" id="speedRange" min="0.25" max="4.0" step="0.25" value="1.0">
    </div>
    
    <div class="config-section">
        <button id="readPage">Read Entire Page</button>
        <button id="readSelection">Read Selection</button>
        <button id="stopReading" class="stop-btn">Stop</button>
    </div>
    
    <div id="status"></div>
    
    <script src="popup.js"></script>
</body>
</html>