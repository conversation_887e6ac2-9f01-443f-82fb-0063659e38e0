// popup.js
document.addEventListener('DOMContentLoaded', function() {
    const apiKeyInput = document.getElementById('apiKey');
    const saveKeyBtn = document.getElementById('saveKey');
    const voiceSelect = document.getElementById('voiceSelect');
    const speedRange = document.getElementById('speedRange');
    const speedValue = document.getElementById('speedValue');
    const readPageBtn = document.getElementById('readPage');
    const readSelectionBtn = document.getElementById('readSelection');
    const pauseBtn = document.getElementById('pauseReading');
    const resumeBtn = document.getElementById('resumeReading');
    const stopBtn = document.getElementById('stopReading');
    const status = document.getElementById('status');

    // Load saved settings
    chrome.storage.sync.get(['apiKey', 'voice', 'speed'], function(result) {
        if (result.apiKey) {
            apiKeyInput.value = result.apiKey;
        }
        if (result.voice) {
            voiceSelect.value = result.voice;
        }
        if (result.speed) {
            speedRange.value = result.speed;
            speedValue.textContent = result.speed;
        }
    });

    // Update speed display
    speedRange.addEventListener('input', function() {
        speedValue.textContent = this.value;
    });

    // Save API key
    saveKeyBtn.addEventListener('click', function() {
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            showStatus('Please enter your API key', 'error');
            return;
        }
        
        chrome.storage.sync.set({
            apiKey: apiKey,
            voice: voiceSelect.value,
            speed: speedRange.value
        }, function() {
            showStatus('Settings saved!', 'success');
        });
    });

    // Read entire page
    readPageBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'readPage',
                voice: voiceSelect.value,
                speed: parseFloat(speedRange.value)
            });
        });
        window.close();
    });

    // Read selection
    readSelectionBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {
                action: 'readSelection',
                voice: voiceSelect.value,
                speed: parseFloat(speedRange.value)
            });
        });
        window.close();
    });

    // Pause reading
    pauseBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'pause'});
        });
        window.close();
    });

    // Resume reading
    resumeBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'resume'});
        });
        window.close();
    });

    // Stop reading
    stopBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'stop'});
        });
        window.close();
    });

    function showStatus(message, type) {
        status.textContent = message;
        status.className = `status ${type}`;
        setTimeout(() => {
            status.textContent = '';
            status.className = 'status';
        }, 3000);
    }
});