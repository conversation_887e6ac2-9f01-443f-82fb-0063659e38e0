// content.js
let currentAudio = null;
let audioQueue = [];
let isPlaying = false;

chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    console.log('Content script received message:', request);
    
    switch(request.action) {
        case 'readPage':
            readEntirePage(request.voice, request.speed);
            sendResponse({success: true});
            break;
        case 'readSelection':
            readSelectedText(request.voice, request.speed);
            sendResponse({success: true});
            break;
        case 'stop':
            stopReading();
            sendResponse({success: true});
            break;
        default:
            sendResponse({success: false, error: 'Unknown action'});
    }
    
    return true;
});

function readEntirePage(voice, speed) {
    console.log('Reading entire page...');
    const text = extractPageText();
    console.log('Extracted text length:', text.length);
    
    if (text.trim()) {
        textToSpeech(text, voice, speed);
    } else {
        alert('No readable text found on this page.');
    }
}

function readSelectedText(voice, speed) {
    console.log('Reading selected text...');
    const selectedText = window.getSelection().toString();
    console.log('Selected text:', selectedText);
    
    if (selectedText.trim()) {
        textToSpeech(selectedText, voice, speed);
    } else {
        alert('Please select some text first.');
    }
}

function extractPageText() {
    const clonedDoc = document.cloneNode(true);
    const scripts = clonedDoc.querySelectorAll('script, style, nav, header, footer, aside');
    scripts.forEach(el => el.remove());
    
    const main = clonedDoc.querySelector('main, article, .content, #content, .post, .entry');
    const textSource = main || clonedDoc.body;
    
    let text = textSource.innerText || textSource.textContent || '';
    
    text = text.replace(/\s+/g, ' ').trim();
    text = text.replace(/[\r\n]+/g, '. ');
    
    return text;
}

async function textToSpeech(text, voice, speed) {
    try {
        console.log('Starting TTS for text:', text.substring(0, 100) + '...');
        stopReading();
        
        const result = await chrome.storage.sync.get(['apiKey']);
        console.log('API key found:', !!result.apiKey);
        
        if (!result.apiKey) {
            alert('Please set your Google Cloud API key in the extension popup.');
            return;
        }
        
        showLoadingIndicator();
        
        const chunks = splitTextIntoChunks(text, 4000);
        console.log('Text split into', chunks.length, 'chunks');
        
        for (let i = 0; i < chunks.length; i++) {
            console.log(`Processing chunk ${i + 1}/${chunks.length}`);
            const audioData = await callGoogleTTS(chunks[i], voice, speed, result.apiKey);
            if (audioData) {
                audioQueue.push(audioData);
            }
        }
        
        hideLoadingIndicator();
        console.log('Starting audio playback, queue length:', audioQueue.length);
        playAudioQueue();
        
    } catch (error) {
        console.error('TTS Error:', error);
        hideLoadingIndicator();
        alert('Error generating speech: ' + error.message);
    }
}

async function callGoogleTTS(text, voice, speed, apiKey) {
    console.log('Calling Google TTS API...');
    
    const response = await fetch(`https://texttospeech.googleapis.com/v1beta1/text:synthesize?key=${apiKey}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            input: { 
                text: text 
            },
            voice: {
                languageCode: voice.split('-').slice(0, 2).join('-'),
                name: voice
            },
            audioConfig: {
                audioEncoding: 'LINEAR16',
                effectsProfileId: [
                    'small-bluetooth-speaker-class-device'
                ],
                pitch: 0,
                speakingRate: speed
            }
        })
    });
    
    console.log('TTS API response status:', response.status);
    
    if (!response.ok) {
        const errorText = await response.text();
        console.error('TTS API error:', errorText);
        throw new Error(`API request failed: ${response.status} - ${errorText}`);
    }
    
    const data = await response.json();
    return data.audioContent;
}

function splitTextIntoChunks(text, maxLength) {
    const chunks = [];
    const sentences = text.split(/[.!?]+/);
    let currentChunk = '';
    
    for (const sentence of sentences) {
        if ((currentChunk + sentence).length > maxLength && currentChunk) {
            chunks.push(currentChunk.trim());
            currentChunk = sentence;
        } else {
            currentChunk += sentence + '. ';
        }
    }
    
    if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
    }
    
    return chunks;
}

function playAudioQueue() {
    if (audioQueue.length === 0 || isPlaying) return;
    
    console.log('Starting audio queue playback');
    isPlaying = true;
    playNextAudio();
}

function playNextAudio() {
    if (audioQueue.length === 0) {
        console.log('Audio queue finished');
        isPlaying = false;
        return;
    }
    
    console.log('Playing next audio, remaining in queue:', audioQueue.length);
    const audioData = audioQueue.shift();
    const audioBlob = base64ToBlob(audioData, 'audio/wav');
    const audioUrl = URL.createObjectURL(audioBlob);
    
    currentAudio = new Audio(audioUrl);
    currentAudio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        playNextAudio();
    };
    currentAudio.onerror = (e) => {
        console.error('Audio playback error:', e);
        URL.revokeObjectURL(audioUrl);
        playNextAudio();
    };
    
    currentAudio.play().catch(e => {
        console.error('Audio play error:', e);
        playNextAudio();
    });
}

function stopReading() {
    console.log('Stopping audio playback');
    if (currentAudio) {
        currentAudio.pause();
        currentAudio = null;
    }
    audioQueue = [];
    isPlaying = false;
    hideLoadingIndicator();
}

function base64ToBlob(base64, type) {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
        bytes[i] = binary.charCodeAt(i);
    }
    return new Blob([bytes], { type: type });
}

function showLoadingIndicator() {
    hideLoadingIndicator();
    
    const loader = document.createElement('div');
    loader.id = 'tts-loader';
    loader.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4285f4;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        z-index: 10000;
        font-family: Arial, sans-serif;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    `;
    loader.textContent = 'Generating speech...';
    document.body.appendChild(loader);
}

function hideLoadingIndicator() {
    const loader = document.getElementById('tts-loader');
    if (loader) {
        loader.remove();
    }
}

console.log('TTS Content script loaded');