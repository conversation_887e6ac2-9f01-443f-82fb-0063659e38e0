// content.js
let currentAudio = null;
let audioQueue = [];
let isPlaying = false;
let isStreaming = false;
let isPaused = false;
let currentChunkIndex = 0;
let textChunks = [];
let highlightedElements = [];
let streamingAborted = false;

chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    console.log('Content script received message:', request);
    
    switch(request.action) {
        case 'readPage':
            readEntirePage(request.voice, request.speed);
            sendResponse({success: true});
            break;
        case 'readSelection':
            readSelectedText(request.voice, request.speed);
            sendResponse({success: true});
            break;
        case 'stop':
            stopReading();
            sendResponse({success: true});
            break;
        case 'pause':
            pauseReading();
            sendResponse({success: true});
            break;
        case 'resume':
            resumeReading();
            sendResponse({success: true});
            break;
        default:
            sendResponse({success: false, error: 'Unknown action'});
    }
    
    return true;
});

function readEntirePage(voice, speed) {
    console.log('Reading entire page...');
    const text = extractPageText();
    console.log('Extracted text length:', text.length);
    
    if (text.trim()) {
        textToSpeech(text, voice, speed);
    } else {
        alert('No readable text found on this page.');
    }
}

function readSelectedText(voice, speed) {
    console.log('Reading selected text...');
    const selection = window.getSelection();
    const selectedText = selection.toString();
    console.log('Selected text:', selectedText);

    if (selectedText.trim()) {
        // Store selection for highlighting
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            // We'll use the selected text directly for highlighting
        }
        textToSpeech(selectedText, voice, speed);
    } else {
        alert('Please select some text first.');
    }
}

function extractPageText() {
    const clonedDoc = document.cloneNode(true);
    const scripts = clonedDoc.querySelectorAll('script, style, nav, header, footer, aside');
    scripts.forEach(el => el.remove());
    
    const main = clonedDoc.querySelector('main, article, .content, #content, .post, .entry');
    const textSource = main || clonedDoc.body;
    
    let text = textSource.innerText || textSource.textContent || '';
    
    text = text.replace(/\s+/g, ' ').trim();
    text = text.replace(/[\r\n]+/g, '. ');
    
    return text;
}

async function textToSpeech(text, voice, speed) {
    try {
        console.log('Starting streaming TTS for text:', text.substring(0, 100) + '...');
        stopReading();

        const result = await chrome.storage.sync.get(['apiKey']);
        console.log('API key found:', !!result.apiKey);

        if (!result.apiKey) {
            alert('Please set your Google Cloud API key in the extension popup.');
            return;
        }

        // Initialize streaming variables
        isStreaming = true;
        streamingAborted = false;
        currentChunkIndex = 0;
        textChunks = splitTextIntoChunks(text, 4000);
        audioQueue = [];

        console.log('Text split into', textChunks.length, 'chunks for streaming');
        showStreamingIndicator(0, textChunks.length);

        // Start streaming: process and play chunks concurrently
        streamAudioChunks(voice, speed, result.apiKey);

    } catch (error) {
        console.error('TTS Error:', error);
        hideLoadingIndicator();
        isStreaming = false;
        alert('Error generating speech: ' + error.message);
    }
}

async function callGoogleTTS(text, voice, speed, apiKey) {
    console.log('Calling Google TTS API...');
    
    const response = await fetch(`https://texttospeech.googleapis.com/v1beta1/text:synthesize?key=${apiKey}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            input: { 
                text: text 
            },
            voice: {
                languageCode: voice.split('-').slice(0, 2).join('-'),
                name: voice
            },
            audioConfig: {
                audioEncoding: 'LINEAR16',
                effectsProfileId: [
                    'small-bluetooth-speaker-class-device'
                ],
                pitch: 0,
                speakingRate: speed
            }
        })
    });
    
    console.log('TTS API response status:', response.status);
    
    if (!response.ok) {
        const errorText = await response.text();
        console.error('TTS API error:', errorText);
        throw new Error(`API request failed: ${response.status} - ${errorText}`);
    }
    
    const data = await response.json();
    return data.audioContent;
}

function splitTextIntoChunks(text, maxLength) {
    const chunks = [];
    const sentences = text.split(/[.!?]+/);
    let currentChunk = '';
    
    for (const sentence of sentences) {
        if ((currentChunk + sentence).length > maxLength && currentChunk) {
            chunks.push(currentChunk.trim());
            currentChunk = sentence;
        } else {
            currentChunk += sentence + '. ';
        }
    }
    
    if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
    }
    
    return chunks;
}

async function streamAudioChunks(voice, speed, apiKey) {
    let processingIndex = 0;
    let playbackStarted = false;

    // Process chunks in parallel with playback
    const processChunk = async (index) => {
        if (streamingAborted) return;

        try {
            console.log(`Processing chunk ${index + 1}/${textChunks.length}`);
            updateStreamingIndicator(index + 1, textChunks.length, 'processing');

            const audioData = await callGoogleTTS(textChunks[index], voice, speed, apiKey);
            if (audioData && !streamingAborted) {
                audioQueue.push({
                    audioData: audioData,
                    chunkIndex: index,
                    text: textChunks[index]
                });

                // Start playback when first chunk is ready
                if (!playbackStarted) {
                    playbackStarted = true;
                    playStreamingAudio();
                }
            }
        } catch (error) {
            console.error(`Error processing chunk ${index + 1}:`, error);
            if (index === 0) {
                // If first chunk fails, show error
                hideLoadingIndicator();
                alert('Error generating speech: ' + error.message);
                isStreaming = false;
            }
        }
    };

    // Process chunks with controlled concurrency (max 2 at a time to avoid API limits)
    const maxConcurrent = 2;
    for (let i = 0; i < textChunks.length; i += maxConcurrent) {
        if (streamingAborted) break;

        const batch = [];
        for (let j = 0; j < maxConcurrent && i + j < textChunks.length; j++) {
            batch.push(processChunk(i + j));
        }
        await Promise.all(batch);
    }
}

function playStreamingAudio() {
    if (isPlaying || streamingAborted) return;

    console.log('Starting streaming audio playback');
    isPlaying = true;
    playNextStreamingAudio();
}

function playNextStreamingAudio() {
    if (streamingAborted) {
        isPlaying = false;
        return;
    }

    // Handle pause state
    if (isPaused) {
        console.log('Playback paused, waiting...');
        setTimeout(() => playNextStreamingAudio(), 100);
        return;
    }

    // Wait for next chunk if queue is empty but streaming is still active
    if (audioQueue.length === 0) {
        if (isStreaming && currentChunkIndex < textChunks.length) {
            console.log('Waiting for next chunk...');
            setTimeout(() => playNextStreamingAudio(), 100);
            return;
        } else {
            console.log('Streaming audio finished');
            isPlaying = false;
            isStreaming = false;
            hideLoadingIndicator();
            clearTextHighlight();
            return;
        }
    }

    const audioItem = audioQueue.shift();
    const audioBlob = base64ToBlob(audioItem.audioData, 'audio/wav');
    const audioUrl = URL.createObjectURL(audioBlob);

    // Highlight current text chunk
    highlightCurrentChunk(audioItem.chunkIndex, audioItem.text);
    updateStreamingIndicator(audioItem.chunkIndex + 1, textChunks.length, 'playing');

    currentAudio = new Audio(audioUrl);
    currentAudio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        currentChunkIndex++;
        if (!isPaused) {
            playNextStreamingAudio();
        }
    };
    currentAudio.onerror = (e) => {
        console.error('Audio playback error:', e);
        URL.revokeObjectURL(audioUrl);
        currentChunkIndex++;
        if (!isPaused) {
            playNextStreamingAudio();
        }
    };

    currentAudio.play().catch(e => {
        console.error('Audio play error:', e);
        currentChunkIndex++;
        if (!isPaused) {
            playNextStreamingAudio();
        }
    });
}

function stopReading() {
    console.log('Stopping audio playback and streaming');
    streamingAborted = true;
    isStreaming = false;
    isPaused = false;

    if (currentAudio) {
        currentAudio.pause();
        currentAudio = null;
    }
    audioQueue = [];
    isPlaying = false;
    currentChunkIndex = 0;
    textChunks = [];

    hideLoadingIndicator();
    clearTextHighlight();
}

function pauseReading() {
    console.log('Pausing audio playback');
    isPaused = true;

    if (currentAudio) {
        currentAudio.pause();
    }

    updateStreamingIndicatorStatus('paused');
}

function resumeReading() {
    console.log('Resuming audio playback');
    isPaused = false;

    if (currentAudio) {
        currentAudio.play().catch(e => {
            console.error('Error resuming audio:', e);
            playNextStreamingAudio();
        });
    } else if (isStreaming) {
        // Resume streaming if it was paused during processing
        playNextStreamingAudio();
    }

    updateStreamingIndicatorStatus('playing');
}

function base64ToBlob(base64, type) {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
        bytes[i] = binary.charCodeAt(i);
    }
    return new Blob([bytes], { type: type });
}

function showStreamingIndicator(current, total) {
    hideLoadingIndicator();

    const loader = document.createElement('div');
    loader.id = 'tts-loader';
    loader.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4285f4;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-family: Arial, sans-serif;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        min-width: 250px;
    `;

    loader.innerHTML = `
        <div style="margin-bottom: 8px; font-weight: bold;">🎵 Streaming TTS</div>
        <div id="tts-progress-text">Preparing chunks...</div>
        <div style="background: rgba(255,255,255,0.3); height: 4px; border-radius: 2px; margin-top: 8px;">
            <div id="tts-progress-bar" style="background: white; height: 100%; border-radius: 2px; width: 0%; transition: width 0.3s;"></div>
        </div>
    `;

    document.body.appendChild(loader);
}

function updateStreamingIndicator(current, total, status) {
    const loader = document.getElementById('tts-loader');
    const progressText = document.getElementById('tts-progress-text');
    const progressBar = document.getElementById('tts-progress-bar');

    if (loader && progressText && progressBar) {
        const percentage = Math.round((current / total) * 100);

        if (status === 'processing') {
            progressText.textContent = `Processing chunk ${current}/${total}`;
        } else if (status === 'playing') {
            progressText.textContent = `🔊 Playing chunk ${current}/${total}`;
        }

        progressBar.style.width = `${percentage}%`;
    }
}

function updateStreamingIndicatorStatus(status) {
    const loader = document.getElementById('tts-loader');
    const progressText = document.getElementById('tts-progress-text');

    if (loader && progressText) {
        if (status === 'paused') {
            progressText.textContent = `⏸️ Paused - ${progressText.textContent.replace(/🔊|⏸️|▶️/g, '').trim()}`;
            loader.style.background = '#ff9800';
        } else if (status === 'playing') {
            progressText.textContent = progressText.textContent.replace(/⏸️|▶️/g, '🔊');
            loader.style.background = '#4285f4';
        }
    }
}

function hideLoadingIndicator() {
    const loader = document.getElementById('tts-loader');
    if (loader) {
        loader.remove();
    }
}

// Text highlighting functions
function highlightCurrentChunk(chunkIndex, chunkText) {
    clearTextHighlight();

    try {
        // Find and highlight the text in the page
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.trim()) {
                textNodes.push(node);
            }
        }

        // Clean the chunk text for comparison
        const cleanChunkText = chunkText.replace(/[^\w\s]/g, '').toLowerCase();
        const words = cleanChunkText.split(/\s+/).filter(word => word.length > 2);

        if (words.length === 0) return;

        // Find text nodes that contain words from our chunk
        let matchingNodes = [];
        let currentMatch = '';
        let matchStartNode = null;
        let matchStartOffset = 0;

        for (let textNode of textNodes) {
            const nodeText = textNode.textContent.replace(/[^\w\s]/g, '').toLowerCase();

            // Check if this node contains significant words from our chunk
            let wordMatches = 0;
            for (let word of words) {
                if (nodeText.includes(word)) {
                    wordMatches++;
                }
            }

            // If we have a good match (at least 30% of words), highlight this area
            if (wordMatches >= Math.max(1, Math.floor(words.length * 0.3))) {
                highlightTextNode(textNode);
                break; // Highlight first good match to avoid over-highlighting
            }
        }

    } catch (error) {
        console.error('Error highlighting text:', error);
    }
}

function highlightTextNode(textNode) {
    try {
        const parent = textNode.parentNode;
        if (!parent || parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE') {
            return;
        }

        // Create highlight wrapper
        const highlight = document.createElement('span');
        highlight.className = 'tts-highlight';
        highlight.style.cssText = `
            background: linear-gradient(120deg, #84fab0 0%, #8fd3f4 100%);
            color: #000;
            padding: 2px 4px;
            border-radius: 3px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            animation: tts-pulse 2s ease-in-out infinite;
        `;

        // Add CSS animation if not already added
        if (!document.getElementById('tts-highlight-styles')) {
            const style = document.createElement('style');
            style.id = 'tts-highlight-styles';
            style.textContent = `
                @keyframes tts-pulse {
                    0%, 100% { opacity: 0.8; transform: scale(1); }
                    50% { opacity: 1; transform: scale(1.02); }
                }
                .tts-highlight {
                    transition: all 0.3s ease;
                }
            `;
            document.head.appendChild(style);
        }

        // Wrap the text node
        parent.insertBefore(highlight, textNode);
        highlight.appendChild(textNode);

        highlightedElements.push(highlight);

        // Scroll to highlight if it's not visible
        setTimeout(() => {
            const rect = highlight.getBoundingClientRect();
            if (rect.top < 0 || rect.bottom > window.innerHeight) {
                highlight.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            }
        }, 100);

    } catch (error) {
        console.error('Error creating highlight:', error);
    }
}

function clearTextHighlight() {
    // Remove all highlights
    highlightedElements.forEach(element => {
        try {
            const parent = element.parentNode;
            if (parent) {
                // Move text node back to original parent
                while (element.firstChild) {
                    parent.insertBefore(element.firstChild, element);
                }
                parent.removeChild(element);
            }
        } catch (error) {
            console.error('Error removing highlight:', error);
        }
    });

    highlightedElements = [];

    // Remove styles
    const styles = document.getElementById('tts-highlight-styles');
    if (styles) {
        styles.remove();
    }
}

console.log('TTS Content script loaded with streaming and highlighting support');